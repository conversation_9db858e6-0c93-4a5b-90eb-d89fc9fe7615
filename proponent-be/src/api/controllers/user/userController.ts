import { Request, Response } from 'express';
import { supabaseAdminClient } from '@/db/supabaseClient';
import { DatabaseResponseError } from '@/errors/databaseResponseError';
import { ValidationError } from '@/errors/validationError';
import { IntegrationService } from '@/db/commonDBTypes';
import { handleProfilePictureUpload } from '@/service/user/handleProfilePictureUpload';
import { getAssetSignedUrlFromS3 } from '@/service/aws/s3';
import { tenantBucket } from '@/service/aws/config';
import { handleRemoveIntegrations } from '@/service/integrations/remove-integration/removeIntegrations';
import { getBaseFrontendUrl } from '@/utils/getBaseFrontendUrl';
import { usersDBServices } from '@/service/user/usersDBServices';
import { invalidateUserAuthCache } from '@/middleware/authenticationMiddleware';

//send post request with email ID to check if user exists. Used mainly for sign up, password reset checks
const checkUserExists = async (req: Request, res: Response) => {
  const email = req.body.email;
  if (typeof email !== 'string') return res.status(400).json({ error: 'Email must be a string' });

  try {
    const exists = await usersDBServices.checkUserExists(email);
    return res.status(200).json({ exists });
  } catch (error) {
    return res.status(500).json({
      error: 'An error occurred while processing the request',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

const getUser = async (req: Request, res: Response) => {
  const { supabase, tenantId, userId } = req.user;
  try {
    const user = await usersDBServices.getUser(userId, tenantId, supabase);
    if (!user) return res.status(404).json({ error: 'User not found' });
    return res.status(200).json(user);
  } catch (error) {
    console.error('error getting user', error);
    throw error;
  }
};

const getUserByEmail = async (req: Request, res: Response) => {
  const { supabase, tenantId } = req.user;
  const { email } = req.params;
  try {
    const user = await usersDBServices.getUserByEmail(email, tenantId, supabase);
    return res.status(200).json(user);
  } catch (error) {
    console.error('error getting user by email', error);
    throw error;
  }
};

const getUsers = async (req: Request, res: Response) => {
  const { supabase, tenantId } = req.user;
  try {
    const users = await usersDBServices.getUsers(tenantId, supabase);
    return res.status(200).json(users);
  } catch (error) {
    console.error('error getting all users', error);
    throw error;
  }
};

const updateUser = async (req: Request, res: Response) => {
  try {
    const { supabase, userId } = req.user;
    if (!userId) throw new ValidationError('userId not provided');

    const { onboardingComplete, email, firstName, lastName, linkedinUrl } = req.body;

    await usersDBServices.updateUser(userId, { onboardingComplete, email, firstName, lastName, linkedinUrl }, supabase);

    // Invalidate cache after user update
    invalidateUserAuthCache(userId);

    return res.status(200).json(true);
  } catch (error) {
    console.error('error updating user', error);
    throw error;
  }
};

const deleteUser = async (req: Request, res: Response) => {
  try {
    const { supabase } = req.user;
    const { email } = req.params;
    if (!email) throw new ValidationError('email not provided');

    const user = await usersDBServices.getUserForDeletion(email, supabase);

    if (!user) return res.status(404).json({ error: 'User not found' });

    await usersDBServices.updateUser(user.userId, { onboardingComplete: false, status: 'deleted' }, supabase);

    invalidateUserAuthCache(user.userId);

    return res.status(200).json({ data: true });
  } catch (error) {
    console.error('error deleting user', error);
    return res.status(401).json({ error: error instanceof Error ? error.message : 'Unknown error' });
  }
};

const removeIntegration = async (req: Request, res: Response) => {
  try {
    const { supabase, userId, tenantId } = req.user;
    const { integrationService } = req.params as { integrationService: IntegrationService };

    if (!integrationService) throw new ValidationError('integrationService not provided');

    await handleRemoveIntegrations({ integrationService, userId, tenantId, supabase });

    return res.status(200).json({ message: 'Integration removed successfully' });
  } catch (error) {
    console.error('error removing integration', error);
    throw error;
  }
};

const getUserIntegrations = async (req: Request, res: Response) => {
  try {
    const { supabase, userId } = req.user;

    const data = await usersDBServices.getUserIntegrations(userId, supabase);
    return res.status(200).json(data);
  } catch (error) {
    console.error('error getting user integrations', error);
    throw error;
  }
};

export const getUserConfigs = async (req: Request, res: Response) => {
  try {
    const { supabase, userId } = req.user;

    const data = await usersDBServices.getUserConfigs(userId, supabase);
    return res.status(200).json(data);
  } catch (error) {
    console.error('error getting user config', error);
    throw error;
  }
};

const updatePassword = async (req: Request, res: Response) => {
  try {
    const { supabase } = req.user;
    const { password, sessionTokens } = req.body;
    if (!password) throw new ValidationError('password not provided');
    const { accessToken, refreshToken } = sessionTokens;
    await supabase.auth.setSession({
      access_token: accessToken,
      refresh_token: refreshToken,
    });

    const { data, error } = await supabase.auth.updateUser({
      password: String(password),
    });
    if (error) throw new DatabaseResponseError('Error from DB while updating password', error);

    return res.status(200).json({ data });
  } catch (error) {
    console.error('error updating password', error);
    throw error;
  }
};

const updateBotConfigs = async (req: Request, res: Response) => {
  try {
    const { supabase, userId } = req.user;
    const { botName, botMessage, botRecordMode } = req.body;
    console.log('🚀 ~ updateBotConfigs ~ botRecordMode:', botRecordMode);

    const data = await usersDBServices.updateBotConfigs(userId, { botName, botMessage, botRecordMode }, supabase);

    return res.status(200).json(data);
  } catch (error) {
    console.error('error updating bot configs', error);
    throw error;
  }
};

const updateUserMetadata = async (req: Request, res: Response) => {
  try {
    const { supabase } = req.user;

    const { metadata, sessionTokens } = req.body;

    const { accessToken, refreshToken } = sessionTokens;

    await supabase.auth.setSession({
      access_token: accessToken,
      refresh_token: refreshToken,
    });

    const { data, error } = await supabase.auth.updateUser({ data: metadata });
    if (error) throw new DatabaseResponseError('Error from db', error);
    return res.status(200).json({ data });
  } catch (error: unknown) {
    console.error('Error from updateUserMetadata', error);
    throw error;
  }
};

const uploadProfilePicture = async (req: Request, res: Response) => {
  const { userMetadata } = res.locals;
  const { supabase, userId } = req.user;

  if (req.fileValidationError) {
    return res.status(400).json({
      error: 'Validation Error',
      message: req.fileValidationError,
    });
  }

  if (!req.file) {
    return res.status(400).json({
      error: 'No file provided',
      message: 'Please provide an image file to upload.',
    });
  }

  try {
    const path = await handleProfilePictureUpload({
      file: req.file,
      userMetadata,
      userId,
      supabase,
    });

    await usersDBServices.updateProfilePicturePath(userId, path, supabase);

    const signedUrl =
      tenantBucket &&
      (await getAssetSignedUrlFromS3({
        objectKey: path,
        bucketName: tenantBucket,
      }));

    return res.status(200).json({ url: signedUrl });
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    throw error;
  }
};

const getProfilePictureUrl = async (req: Request, res: Response) => {
  const { supabase, userId } = req.user;

  try {
    const path = await usersDBServices.getProfilePicturePath(userId, supabase);

    if (!path) return res.status(404).json({ message: 'No profile picture found' });

    const signedUrl =
      tenantBucket &&
      (await getAssetSignedUrlFromS3({
        objectKey: path,
        bucketName: tenantBucket,
      }));

    return res.status(200).json({ url: signedUrl });
  } catch (error) {
    console.error('Error getting profile picture URL:', error);
    throw error;
  }
};

const inviteUser = async (req: Request, res: Response) => {
  const { tenantId, supabase } = req.user;
  const payloadData = req.body;

  if (!Array.isArray(payloadData)) throw new ValidationError('Invalid payload format');

  try {
    const emails = payloadData.map((user) => user.email);
    const existingEmails = await usersDBServices.checkUsersExistForInvitation(emails, supabase);

    if (existingEmails.length > 0) {
      const existingUserEmails = existingEmails.join(', ');
      throw new ValidationError(`User: ${existingUserEmails} already exists`);
    }

    const tenantName = await usersDBServices.getTenantName(tenantId, supabase);

    await Promise.all(
      payloadData.map(async (user) => {
        const { email, role } = user;
        const { error: inviteError } = await supabaseAdminClient.auth.admin.inviteUserByEmail(email, {
          data: {
            role,
            tenantId: tenantId,
            tenantName: tenantName,
          },
          redirectTo: `${getBaseFrontendUrl()}/signup-invite`,
        });
        if (inviteError) throw new DatabaseResponseError('Error from DB while inviting user', inviteError);
      })
    );

    return res.status(200).json({ message: 'Users invited successfully' });
  } catch (error) {
    console.error('Error inviting users:', error);
    throw error;
  }
};

export const userController = {
  checkUserExists,
  updateUser,
  inviteUser,
  deleteUser,
  updateBotConfigs,
  updateUserMetadata,
  uploadProfilePicture,
  getUsers,
  getUser,
  getUserByEmail,
  updatePassword,
  getUserConfigs,
  removeIntegration,
  getUserIntegrations,
  getProfilePictureUrl,
};
