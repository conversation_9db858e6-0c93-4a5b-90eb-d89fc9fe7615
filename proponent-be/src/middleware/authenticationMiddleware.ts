import { Request, Response, NextFunction } from 'express';
import { getSupabaseClientWithUserSession } from '@/db/supabaseClient';
import { getUser } from '@/service/db-calls/getUser';
import { UserMetadata } from '@/types/express';
import { AuthError } from '@supabase/supabase-js';
import { getUserData } from '@/service/user/db/getUserData';

// Simple in-memory cache with TTL
class InMemoryCache<T> {
  private cache = new Map<string, { data: T; expiry: number }>();

  set(key: string, data: T, ttlSeconds: number) {
    const expiry = Date.now() + ttlSeconds * 1000;
    this.cache.set(key, { data, expiry });
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  delete(key: string) {
    this.cache.delete(key);
  }

  // Clean up expired entries periodically
  startCleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, item] of this.cache.entries()) if (now > item.expiry) this.cache.delete(key);
    }, 60000); // Clean every minute
  }
}

// Create cache instance
const userCache = new InMemoryCache<Awaited<ReturnType<typeof getUserData>>>();
userCache.startCleanup();

export const authenticateRequest = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) return res.status(401).json({ error: 'Unauthorized login to supabase auth' });

    const supabase = getSupabaseClientWithUserSession(authHeader);

    const { user } = await getUser(supabase);
    if (!user || !user.email || !user.id) return res.status(401).json({ error: 'User not found, try logging in again' });

    // Try to get user data from cache first
    let tenantUser = userCache.get(user.id);

    if (!tenantUser) {
      // Cache miss - fetch from database
      tenantUser = await getUserData(user.id);

      // Cache for 5 minutes
      userCache.set(user.id, tenantUser, 5 * 60);
    }

    const userMetadata: UserMetadata = {
      email: tenantUser.email,
      userId: user.id,
      firstName: tenantUser.firstName,
      lastName: tenantUser.lastName,
      tenantName: tenantUser.tenantName,
    };

    req.user = {
      role: tenantUser.role,
      tenantId: tenantUser.tenantId,
      supabase: supabase,
      userId: user.id,
    };

    res.locals.userMetadata = userMetadata;

    return next();
  } catch (error: unknown) {
    console.log('Error from supabase auth middleware', error);
    if (error instanceof AuthError && error.status === 401) return res.status(401).json({ error: 'Unauthorized: session not found' });

    if (typeof error === 'object' && error !== null && '__isAuthError' in error && error.__isAuthError === true) return res.status(401).json({ error: 'Unauthorized: session not found' });

    // Log security-related errors
    console.error('Security Error:', {
      timestamp: new Date().toISOString(),
      ip: req.ip,
      path: req.path,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    throw error;
  }
};

// Helper function to invalidate user cache
// Call this when:
// - User updates their profile (name, role changes)
// - User is deactivated/deleted
// - User's tenant changes
// - Admin updates user permissions
export const invalidateUserAuthCache = (userId: string) => {
  userCache.delete(userId);
};
