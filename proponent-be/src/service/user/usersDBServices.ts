import { supabaseAdminClient } from '@/db/supabaseClient';
import { Database } from '@/db/database.types';
import { SupabaseClient } from '@supabase/supabase-js';
import { currentTimestamp } from '@/utils/currentTimestamp';

// Selected user fields type for consistency
interface IUser {
  userId: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  onboardingComplete: boolean;
  tenantId: number;
  linkedinUrl: string | null;
}

interface UserConfigsSelectFields {
  autoJoinMeeting: Database['public']['Enums']['meeting_auto_join_config'];
  postMeetingEmail: Database['public']['Enums']['post_meeting_email_config'];
  aiPrompts: any;
  botName: string;
  botMessage: string;
  botRecordMode: Database['public']['Enums']['bot_record_mode'];
  slackId: string | null;
  slackNotification: boolean;
}

interface UserIntegrationsSelectFields {
  googleCalendar: boolean;
  googleGmail: boolean;
  googleDrive: boolean;
  slack: boolean;
}

interface BotConfigsSelectFields {
  botName: string;
  botMessage: string;
  botRecordMode: Database['public']['Enums']['bot_record_mode'];
}

const SELECT_USER_FIELDS = 'userId: user_id, email, firstName:first_name, lastName:last_name, onboardingComplete: onboarding_complete, tenantId: tenant_id, linkedinUrl: linkedin_url';

// User existence check
const checkUserExists = async (email: string): Promise<boolean> => {
  const { data, error } = await supabaseAdminClient.from('users').select('user_id').eq('email', email);

  if (error) throw new Error(`Database error while checking user existence: ${error.message}`);

  return (data || []).length > 0;
};

// Get all users by tenant
const getUsers = async (tenantId: number, supabase?: SupabaseClient<Database>): Promise<IUser[]> => {
  const client = supabase || supabaseAdminClient;
  const { data, error } = await client.from('users').select(SELECT_USER_FIELDS).eq('tenant_id', tenantId);

  if (error) throw new Error(`Database error while getting users: ${error.message}`);

  return data as IUser[];
};

// Get single user by userId and tenantId
const getUser = async (userId: string, tenantId: number, supabase: SupabaseClient<Database>): Promise<IUser | null> => {
  const { data, error } = await supabase.from('users').select(SELECT_USER_FIELDS).eq('tenant_id', tenantId).eq('user_id', userId).maybeSingle();

  if (error) throw new Error(`Database error while getting user: ${error.message}`);

  return data as IUser | null;
};

// Get user by email and tenantId
const getUserByEmail = async (email: string, tenantId: number, supabase: SupabaseClient<Database>): Promise<IUser | null> => {
  const { data, error } = await supabase.from('users').select(SELECT_USER_FIELDS).eq('email', email).eq('tenant_id', tenantId).maybeSingle();

  if (error) throw new Error(`Database error while getting user by email: ${error.message}`);

  return data as IUser | null;
};

// Update user information
const updateUser = async (
  userId: string,
  updateData: {
    onboardingComplete?: boolean;
    email?: string;
    firstName?: string;
    lastName?: string;
    linkedinUrl?: string;
    status?: Database['public']['Enums']['status'];
  },
  supabase: SupabaseClient<Database>
): Promise<void> => {
  const { error } = await supabase
    .from('users')
    .update({
      onboarding_complete: updateData.onboardingComplete,
      first_name: updateData.firstName,
      last_name: updateData.lastName,
      linkedin_url: updateData.linkedinUrl,
      email: updateData.email,
      updated_at: currentTimestamp(),
    })
    .eq('user_id', userId);

  if (error) throw new Error(`Database error while updating user: ${error.message}`);
};

// Get user by email for deletion/verification
const getUserForDeletion = async (email: string, supabase: SupabaseClient<Database>): Promise<IUser | null> => {
  const { data, error } = await supabase.from('users').select(SELECT_USER_FIELDS).eq('email', email).maybeSingle();

  if (error) throw new Error(`Database error while getting user for deletion: ${error.message}`);

  return data as IUser | null;
};

// Get user integrations
const getUserIntegrations = async (userId: string, supabase: SupabaseClient<Database>): Promise<UserIntegrationsSelectFields | null> => {
  const { data, error } = await supabase
    .from('user_integrations')
    .select('googleCalendar:google_calendar, googleGmail:google_gmail, googleDrive:google_drive, slack')
    .eq('user_id', userId)
    .maybeSingle();

  if (error) throw new Error(`Database error while getting user integrations: ${error.message}`);

  return data as UserIntegrationsSelectFields | null;
};

// Get user configurations
const getUserConfigs = async (userId: string, supabase: SupabaseClient<Database>): Promise<UserConfigsSelectFields | null> => {
  const { data, error } = await supabase
    .from('user_configs')
    .select(
      'autoJoinMeeting:auto_join_meeting,postMeetingEmail: post_meeting_email, aiPrompts: ai_prompts, botName:bot_name, botMessage:bot_message, botRecordMode:bot_record_mode, slackId: slack_id, slackNotification: slack_notification'
    )
    .eq('user_id', userId)
    .maybeSingle();

  if (error) throw new Error(`Database error while getting user configs: ${error.message}`);

  return data as UserConfigsSelectFields | null;
};

// Update bot configurations
const updateBotConfigs = async (
  userId: string,
  botConfigs: {
    botName: string;
    botMessage: string;
    botRecordMode: Database['public']['Enums']['bot_record_mode'];
  },
  supabase: SupabaseClient<Database>
): Promise<BotConfigsSelectFields> => {
  const { data, error } = await supabase
    .from('user_configs')
    .update({
      bot_name: botConfigs.botName,
      bot_message: botConfigs.botMessage,
      bot_record_mode: botConfigs.botRecordMode,
    })
    .eq('user_id', userId)
    .select('botName: bot_name, botMessage:bot_message, botRecordMode: bot_record_mode')
    .single();

  if (error) throw new Error(`Database error while updating bot configs: ${error.message}`);

  return data as BotConfigsSelectFields;
};

// Get profile picture path
const getProfilePicturePath = async (userId: string, supabase: SupabaseClient<Database>): Promise<string | null> => {
  const { data, error } = await supabase.from('users').select('path: profile_pic_path').eq('user_id', userId).maybeSingle();

  if (error) throw new Error(`Database error while getting profile picture path: ${error.message}`);

  return data?.path || null;
};

// Update profile picture path
const updateProfilePicturePath = async (userId: string, path: string, supabase: SupabaseClient<Database>): Promise<void> => {
  const { error } = await supabase.from('users').update({ profile_pic_path: path }).eq('user_id', userId);

  if (error) throw new Error(`Database error while updating profile picture path: ${error.message}`);
};

// Check if users exist for invitation (batch check)
const checkUsersExistForInvitation = async (emails: string[], supabase: SupabaseClient<Database>): Promise<string[]> => {
  const { data, error } = await supabase.from('users').select('email').in('email', emails);

  if (error) throw new Error(`Database error while checking users for invitation: ${error.message}`);

  return (data || []).map((user) => user.email);
};

// Get tenant name for invitation
const getTenantName = async (tenantId: number, supabase: SupabaseClient<Database>): Promise<string> => {
  const { data, error } = await supabase.from('tenants').select('name').eq('id', tenantId).single();

  if (error) throw new Error(`Database error while getting tenant name: ${error.message}`);

  return data.name;
};

export const usersDBServices = {
  checkUserExists,
  getUsers,
  getUser,
  getUserByEmail,
  updateUser,
  getUserForDeletion,
  getUserIntegrations,
  getUserConfigs,
  updateBotConfigs,
  getProfilePicturePath,
  updateProfilePicturePath,
  checkUsersExistForInvitation,
  getTenantName,
};
