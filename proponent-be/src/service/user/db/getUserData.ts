import { Role } from '@/db/commonDBTypes';
import { supabaseAdminClient } from '@/db/supabaseClient';
import { DatabaseResponseError } from '@/errors/databaseResponseError';

export const getUserData = async (
  userId: string
): Promise<{
  email: string;
  tenantId: number;
  firstName: string;
  lastName: string;
  tenantName: string;
  role: Role;
}> => {
  const { data: userEmailData, error: userEmailError } = await supabaseAdminClient
    .from('users')
    .select('email,tenantId: tenant_id, firstName:first_name, lastName:last_name, role, tenant_id (name)')
    .eq('user_id', userId)
    .single();
  if (userEmailError || !userEmailData) throw new DatabaseResponseError('Error fetching user email', userEmailError);

  return {
    email: userEmailData.email,
    tenantId: userEmailData.tenantId,
    firstName: userEmailData?.firstName || '',
    lastName: userEmailData?.lastName || '',
    role: userEmailData.role,
    tenantName: userEmailData.tenant_id?.name || '',
  };
};
