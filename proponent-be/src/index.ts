import 'dotenv/config';
import './service/sentry/instrument';
import 'express-async-errors';
import express, { Express, NextFunction, Request, Response } from 'express';
import * as Sentry from '@sentry/node';
import { NotFoundError } from './errors/notFoundError';
import { errorHandler } from './middleware/errorHandler';
import { versionRouter } from './api/routes/versionRouter';
import { getBaseBackendUrl } from './utils/getBaseBackendUrl';
import allowCors from './middleware/allowCorsMiddleware';
import { initializeScheduler } from './service/crons/cronScheduler';
import { initializeSalesforceCDCSubscriptions, shutdownSalesforceCDC, getCDCHealthStatus } from './service/crm/salesforce/webhook/initializeSalesforceCDCSubscriptions';

// setup express app

const app: Express = express();

const port = process.env.PORT || 3001;

const logger = (req: Request, _: Response, next: NextFunction) => {
  if (!req.method.startsWith('OPTIONS')) console.log(req.method, req.url.substring(0, 60));
  next();
};

// Set up body parsers with appropriate limits
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

app.use(logger);
app.use(allowCors);

app.use('/api', versionRouter);

// health check
app.get('/health', (_: Request, res: Response) => {
  res.send('OK');
});

// CDC health check
app.get('/health/cdc', (_: Request, res: Response) => {
  const cdcStatus = getCDCHealthStatus();
  const isHealthy = Object.values(cdcStatus).every((status) => status === true);

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    cdc: cdcStatus,
    timestamp: new Date().toISOString(),
  });
});

app.all('*', (req: Request, _: Response, next: NextFunction) => {
  console.error('Route not found', req.originalUrl);
  next(new NotFoundError('Route not found'));
});

Sentry.setupExpressErrorHandler(app);
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  errorHandler(err, req, res, next);
});

// Initialize services
const initializeServices = async () => {
  try {
    // Initialize cron scheduler
    await initializeScheduler();

    // Initialize Salesforce CDC subscriptions
    await initializeSalesforceCDCSubscriptions();

    console.log('All services initialized successfully');
  } catch (error) {
    console.error('Error initializing services:', error);
    // Don't exit the process - let the app start even if some services fail
  }
};

// Graceful shutdown handler
const gracefulShutdown = async (signal: string) => {
  console.log(`Received ${signal}. Starting graceful shutdown...`);
   let exitCode = 0;
  try {
    // Shutdown Salesforce CDC subscriptions
    await shutdownSalesforceCDC();
    console.log('Graceful shutdown completed');
  } catch (error) {
    console.error('Error during graceful shutdown:', error);
    exitCode = 1;
  }

  process.exit(exitCode);
};

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server and initialize services
app.listen(port, () => {
  console.log(`Server running at ${getBaseBackendUrl()}`);

  // Initialize services after server starts
  initializeServices();
});
